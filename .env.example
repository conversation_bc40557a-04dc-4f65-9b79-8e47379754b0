# Example environment variables for Vite
# Copy this file to .env.local and fill in the values

# The Graph API key
VITE_GRAPH_API_KEY=your-api-key-here

# Environment setting (local, preview, production)
# This controls which networks and endpoints are used for various services
# Leave empty for local development, set to 'preview' for preview deployments,
# or 'production' for production deployments
#
# Network Configuration:
# - Dev/Preview (empty or 'preview'): Arbitrum Sepolia only
# - Production ('production'): Arbitrum One only
#
# Subgraph Configuration:
# - Dev/Preview: Arbitrum Sepolia subgraph
# - Production: Arbitrum One subgraph
VITE_APP_ENV=

# Supabase configuration
SUPABASE_ANON_KEY=your-supabase-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key-here
SUPABASE_URL=https://${PROJECT_ID}.supabase.co
