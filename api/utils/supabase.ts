import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../../src/types/database/supabase';

export async function createSupabaseServerClient(): Promise<
  SupabaseClient<Database>
> {
  const supabaseUrl = process.env.SUPABASE_URL;
  const supabaseAnonKey = process.env.SUPABASE_ANON_KEY;
  const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl) {
    throw new Error('Missing SUPABASE_URL environment variable.');
  }
  if (!supabaseAnonKey) {
    throw new Error('Missing SUPABASE_ANON_KEY environment variable.');
  }

  // Use service role key for server-side operations to bypass RLS
  // Fall back to anon key if service role key is not available
  const key = supabaseServiceRoleKey || supabaseAnonKey;

  return createClient<Database>(supabaseUrl, key, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
}
